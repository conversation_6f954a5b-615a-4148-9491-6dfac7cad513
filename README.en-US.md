<div align="center">
  <img alt="vue3-element-admin" width="80" height="80" src="./src/assets/logo.png">
  <h1>vue3-element-admin</h1>

  <img src="https://img.shields.io/badge/Vue-3.5.13-brightgreen.svg"/>
  <img src="https://img.shields.io/badge/Vite-6.2.2-green.svg"/>
  <img src="https://img.shields.io/badge/Element Plus-2.9.9-blue.svg"/>
  <img src="https://img.shields.io/badge/license-MIT-green.svg"/>
  <a href="https://gitee.com/youlaiorg" target="_blank">
      <img src="https://img.shields.io/badge/Author-有来开源组织-orange.svg"/>
  </a>

  <a href="https://gitee.com/youlaiorg/vue3-element-admin" target="_blank">
    <img alt="有来技术" src="https://gitee.com/youlaiorg/vue3-element-admin/badge/star.svg"/>
   </a>
  <a href="https://github.com/youlaitech/vue3-element-admin" target="_blank">
    <img alt="有来技术" src="https://img.shields.io/github/stars/youlaitech/vue3-element-admin.svg?style=social&label=Stars"/>
  </a>
  <a href="https://gitcode.com/youlai/vue3-element-admin" target="_blank">
    <img alt="有来技术" src="https://gitcode.com/youlai/vue3-element-admin/star/badge.svg"/>
  </a>

</div>

![](https://foruda.gitee.com/images/1708618984641188532/a7cca095_716974.png "rainbow.png")


<div align="center">
  <a target="_blank" href="https://vue.youlai.tech">🖥️ Live Preview</a> | <a target="_blank" href="https://app.youlai.tech">📲 Mobile Preview</a> |  <a target="_blank" href="https://juejin.cn/post/7228990409909108793">📑 Documentation</a>|  <a target="_blank" href="https://www.youlai.tech//vue3-element-admin">🌐 Official Website</a> | <a href="./README.md">💬 中文
</div>


## Introduction

[vue3-element-admin](https://gitcode.com/youlai/vue3-element-admin) is a minimalist enterprise-level backend management frontend template built with Vue3, Vite, TypeScript, and Element-Plus. It comes with complementary Java backend [youlai-boot](https://gitee.com/youlaiorg/youlai-boot) and Node backend [youlai-nest](https://gitee.com/youlaiorg/youlai-nest). A simplified version [vue3-element-template](https://gitee.com/youlaiorg/vue3-element-template) and a JavaScript version [vue3-element-admin-js](https://gitee.com/youlaiorg/vue3-element-admin) are also available for developers to quickly start development.


## Project Features

- **Simple and Easy-to-use**: Upgraded version of [vue-element-admin](https://gitee.com/panjiachen/vue-element-admin) for Vue3, with minimal encapsulation and easy to get started.
- **Data Interaction**: Support for `Mock` data and [online API documentation](https://www.apifox.cn/apidoc/shared-195e783f-4d85-4235-a038-eec696de4ea5), with accompanying [Java](https://gitee.com/youlaiorg/youlai-boot) and [Node](https://gitee.com/youlaiorg/youlai-nest) backend source code.

- **System Functions**: Provides user management, role management, menu management, department management, dictionary management, and other functional modules.
- **Permission Management**: Supports dynamic routing, button permissions, role permissions, and data permissions.

- **Infrastructure**: Provides internationalization, multiple layouts, dark mode, full screen, watermark, API documentation, and code generator functionality.
- **Continuous Updates**: Project is continuously updated with real-time updates of tools and dependencies.


## Project Screenshots

🖥️ **Dashboard**

![](https://www.youlai.tech/storage/blog/2025/04/30/**************.png)

⚡**API Documentation**

![](https://www.youlai.tech/storage/blog/2025/01/18/**************.png)

📲 **Mobile Version**

![](https://www.youlai.tech/storage/blog/2025/04/30/app.jpg)

## Project Source Code

| Project | Gitee   | Github    | GitCode|
| ---- | ----| ---- | ---- |
| vue3-element-admin ✅| [vue3-element-admin](https://gitee.com/youlaiorg/vue3-element-admin) | [vue3-element-admin](https://github.com/youlaitech/vue3-element-admin) | [vue3-element-admin](https://gitcode.com/youlai/vue3-element-admin) |
| vue3-element-admin JS Version| [vue3-element-admin-js](https://gitee.com/youlaiorg/vue3-element-admin-js) | [vue3-element-admin-js](https://github.com/youlaitech/vue3-element-admin-js) | [vue3-element-admin-js](https://gitcode.com/youlai/vue3-element-admin-js) |
| vue3-element-admin Lite Version | [vue3-element-template](https://gitee.com/youlaiorg/vue3-element-template) | [vue3-element-template](https://github.com/youlaitech/vue3-element-template) |[vue3-element-template](https://gitcode.com/youlai/vue3-element-template)|
| vue-uniapp-admin Mobile Version | [vue-uniapp-admin](https://gitee.com/youlaiorg/vue-uniapp-admin) | [vue-uniapp-admin](https://github.com/youlaitech/vue-uniapp-admin) |[vue-uniapp-admin](https://gitcode.com/youlai/vue-uniapp-admin)|
| Java Backend | [youlai-boot](https://gitee.com/youlaiorg/youlai-boot)       | [youlai-boot](https://github.com/haoxianrui/youlai-boot.git) |[youlai-boot](https://gitcode.com/youlai/youlai-boot.git)|
| Node Backend | [youlai-nest](https://gitee.com/youlaiorg/youlai-nest)       | [youlai-nest](https://github.com/haoxianrui/youlai-nest.git) |[youlai-nest](https://gitcode.com/youlai/youlai-nest.git)|



## Development Guide

| Name          | Link     |
|---------------|--------------------|
| Video Tutorial | [https://www.bilibili.com/video/BV1eFUuYyEFj](https://www.bilibili.com/video/BV1eFUuYyEFj)  |
| Project Setup  | [Building a Backend Management System from Scratch with Vue3, Vite, TypeScript, and Element-Plus](https://blog.csdn.net/u013737132/article/details/130191394)  |
| Official Documentation | [https://www.youlai.tech/vue3-element-admin/](https://www.youlai.tech/vue3-element-admin/)  |
| Code Standards     | [ESLint V9 + Prettier + Stylelint + EditorConfig for Standardized and Unified Frontend Code Style](https://youlai.blog.csdn.net/article/details/145608723) |
| Commit Standards | [Husky + Lint-staged + Commitlint + Commitizen + cz-git for Git Commit Standards](https://youlai.blog.csdn.net/article/details/145615236) |
| API Documentation | [https://www.apifox.cn/apidoc/shared-195e783f-4d85-4235-a038-eec696de4ea5](https://www.apifox.cn/apidoc/shared-195e783f-4d85-4235-a038-eec696de4ea5) |



## Project Setup


- **Environment Preparation**

| Environment Type | Name                     |
|----------------|-----------------------------|
| **Development Tool**   | [Visual Studio Code](https://code.visualstudio.com/Download) |
| **Runtime Environment**   | Node 18 + (Recommended [22.9.0](https://npmmirror.com/mirrors/node/v22.9.0/))  |
> ⚠️ Note: Node.js version 20.6.0 has compatibility issues, please don't use it


- **Quick Start**

```bash
# Clone repository
git clone https://gitee.com/youlaiorg/vue3-element-admin.git

# Change directory
cd vue3-element-admin

# Install pnpm
npm install pnpm -g

# Set mirror source (optional)
pnpm config set registry https://registry.npmmirror.com

# Install dependencies
pnpm install

# Start development server
pnpm run dev
```


## Project Deployment

After executing the `pnpm run build` command, the project will be bundled and a `dist` directory will be generated. Next, upload the files from the `dist` directory to the `/usr/share/nginx/html` directory on your server and configure Nginx for reverse proxy.

```bash
pnpm run build
```

Here is an example Nginx configuration:

```nginx
server {
    listen      80;
    server_name localhost;

    location / {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
    }

    # Reverse proxy configuration
    location /prod-api/ {
        # Please replace api.youlai.tech with your backend API address, and keep the trailing slash /
        proxy_pass http://api.youlai.tech/;
    }
}
```

For more detailed information, please refer to this article: [Nginx Installation and Configuration](https://blog.csdn.net/u013737132/article/details/145667694).

## Local Mock

The project supports both online and local Mock interfaces. By default, it uses online interfaces. To switch to Mock interfaces, modify the `VITE_MOCK_DEV_SERVER` value in the `.env.development` file to `true`.

## Backend API

> If you have a basic understanding of Java development, follow these steps to convert online API to local backend API and create an enterprise-level full-stack development environment to help you on your full-stack journey.

1. Get the backend source code based on `Java` and `SpringBoot` from [youlai-boot](https://gitee.com/youlaiorg/youlai-boot.git).
2. Follow the instructions in the backend project's README.md to [set up and run locally](https://gitee.com/youlaiorg/youlai-boot#%E9%A1%B9%E7%9B%AE%E8%BF%90%E8%A1%8C).
3. Modify the value of `VITE_APP_API_URL` in the `.env.development` file, changing it from https://api.youlai.tech to http://localhost:8989.


## Notes

- **Auto import plugin is disabled by default**

  Component type declarations have been automatically generated for the template project. If you add and use new components, follow the instructions in the screenshot to enable automatic generation. After automatic generation is complete, remember to set it back to `false` to avoid conflicts.

  ![](https://foruda.gitee.com/images/1687755823137387608/412ea803_716974.png)

- **Blank page when accessing the project**

  Try upgrading your browser, as older browser engines may not support certain new JavaScript syntax, such as optional chaining operator `?.`.

- **Project synchronization with repository updates**

  After synchronizing the project with repository updates, it is recommended to run `pnpm install` to update dependencies before starting.

- **Red highlight on project components, functions, and imports**

  Restart VSCode to try again.

- **Other issues**

  If you have any other issues or suggestions, please open an [ISSUE](https://gitee.com/youlaiorg/vue3-element-admin/issues/new).


## Commit Conventions

Execute `pnpm run commit` to invoke interactive git commit and complete the information input and selection according to the prompts.

![](https://foruda.gitee.com/images/1687755823165218215/c1705416_716974.png)


## Project Statistics

![](https://repobeats.axiom.co/api/embed/aa7cca3d6fa9c308fc659fa6e09af9a1910506c3.svg "Repobeats analytics image")


Thanks to all the contributors!

[![contributors](https://contrib.rocks/image?repo=youlaitech/vue3-element-admin)](https://github.com/youlaitech/vue3-element-admin/graphs/contributors)


## Special Thanks

- Thanks to the [GitCode](https://gitcode.com/) official [G-Star](https://gitcode.com/g-star) certification
  ![](https://foruda.gitee.com/images/1728577513089814203/95f2a70d_716974.jpeg)

## Community

① Follow the "Youlai Tech" WeChat Official Account, click the **Group Chat** menu to get the QR code (this measure is taken to prevent ads from entering the group, thanks for your understanding and support).

② Directly add WeChat **`haoxianrui`** with a note indicating "Frontend/Backend/Full Stack".

![Youlai Tech WeChat Official Account](https://foruda.gitee.com/images/1737108820762592766/3390ed0d_716974.png)

