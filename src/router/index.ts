import type { App } from "vue";
import { createRouter, createWebHashHistory, type RouteRecordRaw } from "vue-router";

export const Layout = () => import("@/layout/index.vue");

// 静态路由
export const constantRoutes: RouteRecordRaw[] = [
  {
    path: "/redirect",
    component: Layout,
    meta: { hidden: true },
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },

  {
    path: "/login",
    component: () => import("@/views/login/index.vue"),
    meta: { hidden: true },
  },

  {
    path: "/",
    name: "/",
    component: Layout,
    redirect: "/order/index",
    children: [
      {
        path: "dashboard",
        component: () => import("@/views/dashboard/index.vue"),
        // 用于 keep-alive 功能，需要与 SFC 中自动推导或显式声明的组件名称一致
        // 参考文档: https://cn.vuejs.org/guide/built-ins/keep-alive.html#include-exclude
        name: "Dashboard",
        meta: {
          title: "dashboard",
          icon: "homepage",
          affix: true,
          hidden: true,
          keepAlive: true,
        },
      },
      {
        path: "401",
        component: () => import("@/views/error/401.vue"),
        meta: { hidden: true },
      },
      {
        path: "404",
        component: () => import("@/views/error/404.vue"),
        meta: { hidden: true },
      },
    ],
  },

  {
    path: "/order",
    component: Layout,
    redirect: "/order/index",
    name: "Order",
    meta: {
      title: "订单管理",
      icon: "el-icon-shopping-cart",
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/order/index.vue"),
        name: "OrderIndex",
        meta: {
          title: "订单列表",
          icon: "el-icon-list",
          keepAlive: true,
        },
      },
    ],
  },

  {
    path: "/user",
    component: Layout,
    redirect: "/user/index",
    name: "User",
    meta: {
      title: "用户管理",
      icon: "el-icon-user",
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/user/index.vue"),
        name: "UserIndex",
        meta: {
          title: "用户列表",
          icon: "el-icon-user",
          keepAlive: true,
        },
      },
    ],
  },
  {
    path: "/autoreport",
    component: Layout,
    redirect: "/autoreport/index",
    name: "Autoreport",
    meta: {
      title: "自动报告",
      icon: "el-icon-memo",
    },
    children: [
      {
        path: "index",
        component: () => import("@/views/autoreport/index.vue"),
        name: "AutoreportIndex",
        meta: {
          title: "自动报告",
          icon: "el-icon-memo",
          keepAlive: true,
        },
      },
    ],
  },
];

/**
 * 创建路由
 */
const router = createRouter({
  history: createWebHashHistory(),
  routes: constantRoutes,
  // 刷新时，滚动条位置还原
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

// 全局注册 router
export function setupRouter(app: App<Element>) {
  app.use(router);
}

export default router;
