import request from "@/utils/request";
import md5 from "md5";

const AuthAPI = {
  /** 登录接口*/
  login(data: LoginFormData) {
    const requestParams = {
      username: data.username,
      password: md5(data.password),
    };
    return request<any, string>({
      url: `/api/admin/login`,
      method: "post",
      data: requestParams,
    });
  },
};

export default AuthAPI;

/** 登录表单数据 */
export interface LoginFormData {
  /** 用户名 */
  username: string;
  /** 密码 */
  password: string;
}
