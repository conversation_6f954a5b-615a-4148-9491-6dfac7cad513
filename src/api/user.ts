import request from "@/utils/request";

const UserAPI = {
  getAdminUserList(params: AdminUserRequest) {
    return request<any, any>({
      url: "/api/admin/user/selectList",
      method: "get",
      params,
    });
  },
  getAdminUserIntegralList(params: AdminUserIntegralRequest) {
    return request<any, any>({
      url: "/api/admin/user/selectUserIntegralList",
      method: "get",
      params,
    });
  },
  // 更新用户积分
  updateUserIntegralApi(data: { userId: number; value: number; remark: string }) {
    return request<any, any>({
      url: "/api/admin/user/updateUserIntegral",
      method: "put",
      data,
    });
  },
  /**
   * 获取用户列表
   * @returns
   */
  getUserList() {
    return request<any, any>({
      url: "/api/admin/user/userList",
      method: "get",
    });
  },
};

export default UserAPI;

export interface AdminUserRequest extends PageQuery {
  nickName?: string;
}

export interface AdminUserIntegralRequest extends PageQuery {
  userId?: number;
}

// 获取用户积分记录
export function getUserPointsRecord(params: { pageNum: number; pageSize: number; userId: number }) {
  return request.get("/admin/user/points/record", { params });
}
