import request from "@/utils/request";

export interface ReportListParams {
  pageNum: number;
  pageSize: number;
  kitsNo?: string;
  organization?: string;
  status?: string;
  petNo?: string;
}
const AutoReportAPI = {
  getReportList(params: ReportListParams) {
    return request<any, any>({
      url: "/api/admin/autoReport/selectList",
      method: "get",
      params,
    });
  },
  getReportDetails(id: number) {
    return request<any, any[]>({
      url: "/api/admin/autoReport/details",
      method: "get",
      params: { id },
    });
  },
};

export default AutoReportAPI;
