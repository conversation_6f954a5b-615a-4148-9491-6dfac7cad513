import request from "@/utils/request";

const OrderAPI = {
  getOrderList(params: OrderListParams) {
    return request<any, any>({
      url: "/api/admin/order/selectList",
      method: "get",
      params,
    });
  },
  getOrderDetails(orderNo: string) {
    return request<any, any[]>({
      url: "/api/admin/order/detail",
      method: "get",
      params: { orderNo },
    });
  },

  getBinList(params: BinListParams) {
    return request<any, any>({
      url: "/api/admin/order/binderList",
      method: "get",
      params,
    });
  },

  getLogistics(logistics: string) {
    return request<any, any>({
      url: "/api/admin/order/express/routers",
      method: "get",
      params: { logistics },
    });
  },
  /**
   * 获取报告详情
   * @param id 宠物绑定 ID
   * @returns 报告详情
   */
  getReport(id: string | number) {
    return request<any, any>({
      url: "/api/admin/order/report",
      method: "get",
      params: { id },
    });
  },
  /**
   * 收货成功
   * @param id 宠物绑定 ID
   */
  pickUp(id: string | number) {
    return request<any, boolean>({
      url: `/api/admin/order/pickUp/${id}`,
      method: "post",
    });
  },

  // 获取品种列表
  getVarietiesList() {
    return request<any, any[]>({
      url: "/api/admin/order/selectListPetClassify",
      method: "get",
    });
  },

  // 更新宠物品种
  updatePetVarieties(data: any) {
    return request<any, boolean>({
      url: "/api/admin/order/varieties/update",
      method: "post",
      data,
    });
  },
};

export default OrderAPI;

export interface OrderListParams {
  pageNum: number;
  pageSize: number;
  orderNo?: string;
  payStatus?: string;
  orderStatus?: string;
  logistics?: string;
  userId?: number;
}

export interface BinListParams extends PageQuery {
  orderDetailId?: number;
}
