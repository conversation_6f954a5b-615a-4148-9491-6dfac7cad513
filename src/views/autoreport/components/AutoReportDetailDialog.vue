<template>
  <el-drawer
    :model-value="visible"
    title="报告明细"
    size="1200px"
    :show-close="true"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    :destroy-on-close="true"
    @close="$emit('close')"
  >
    <div class="detail-container" v-loading="loading">
      <!-- 查询区域 -->
      <div class="search-section">
        <el-form :model="searchForm" :inline="true" label-width="auto" size="small">
          <el-form-item label="检测项目">
            <el-input
              v-model="searchForm.item"
              placeholder="搜索检测项目"
              clearable
              style="width: 200px"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="位点">
            <el-input
              v-model="searchForm.position"
              placeholder="搜索位点"
              clearable
              style="width: 200px"
              @input="handleSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleResetSearch">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 搜索结果提示 -->
        <div class="search-result" v-if="searchForm.item || searchForm.position">
          <el-tag type="info" effect="plain" size="small">
            已筛选 {{ filteredDetailList.length }} 条数据
          </el-tag>
        </div>
      </div>

      <el-table
        :data="filteredDetailList"
        border
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
        max-height="600"
      >
        <el-table-column prop="item" label="检测项目" min-width="150" show-overflow-tooltip />
        <el-table-column prop="position" label="位点" min-width="120" show-overflow-tooltip />
        <el-table-column prop="result" label="基因型" min-width="120" show-overflow-tooltip />
        <el-table-column prop="coat" label="毛色表现" min-width="120" show-overflow-tooltip />
        <el-table-column
          prop="resultInterpretation"
          label="结果解读"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column label="报告链接" width="120" align="center">
          <template #default="scope">
            <el-button
              v-if="scope.row.reportUrl"
              type="primary"
              size="small"
              link
              @click="openReport(scope.row.reportUrl)"
            >
              查看报告
            </el-button>
            <span v-else class="no-report">无报告</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 图片预览对话框 -->
    <el-dialog
      v-model="imageVisible"
      title="图片预览"
      width="400px"
      top="5vh"
      :show-close="true"
      :close-on-click-modal="true"
      :close-on-press-escape="true"
    >
      <div class="image-preview-container">
        <el-image
          :src="currentImageUrl"
          fit="contain"
          style="width: 100%; height: 500px"
          :preview-src-list="[currentImageUrl]"
          :initial-index="0"
          preview-teleported
        >
          <template #error>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
              <span>图片加载失败</span>
            </div>
          </template>
        </el-image>
      </div>
    </el-dialog>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watchEffect, computed } from "vue";
import { Picture, Search } from "@element-plus/icons-vue";
import AutoReportAPI from "@/api/autoreport";

defineOptions({
  name: "AutoReportDetailDialog",
  inheritAttrs: false,
});

const props = defineProps<{
  visible: boolean;
  reportId: number | string;
}>();

// 移除未使用的 emit，因为直接使用 $emit('close')

const loading = ref(false);
const detailList = ref<any[]>([]);

// 搜索相关
const searchForm = ref({
  item: "",
  position: "",
});

// 过滤后的数据
const filteredDetailList = computed(() => {
  let result = [...detailList.value];

  if (searchForm.value.item) {
    result = result.filter((item) =>
      item.item?.toLowerCase().includes(searchForm.value.item.toLowerCase())
    );
  }

  if (searchForm.value.position) {
    result = result.filter((item) =>
      item.position?.toLowerCase().includes(searchForm.value.position.toLowerCase())
    );
  }

  return result;
});

// 获取报告详情
const fetchReportDetail = async () => {
  if (!props.reportId) return;

  loading.value = true;
  try {
    const res = await AutoReportAPI.getReportDetails(Number(props.reportId));
    detailList.value = res || [];
  } catch (error) {
    console.error("获取报告详情失败:", error);
  } finally {
    loading.value = false;
  }
};

// 删除未使用的状态函数，因为基本信息部分已被移除

// 搜索和重置函数
const handleSearch = () => {
  // 实时搜索，computed 会自动更新
};

const handleResetSearch = () => {
  searchForm.value = {
    item: "",
    position: "",
  };
};

// 图片预览相关
const imageVisible = ref(false);
const currentImageUrl = ref("");

// 打开报告链接
const openReport = (url: string) => {
  if (url) {
    // 检查是否是图片链接
    const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"];
    const isImage = imageExtensions.some((ext) => url.toLowerCase().includes(ext));

    if (isImage) {
      // 如果是图片，在当前页面预览
      currentImageUrl.value = url;
      imageVisible.value = true;
    } else {
      // 如果不是图片，在新窗口打开
      window.open(url, "_blank");
    }
  }
};

// 监听对话框显示状态
watchEffect(() => {
  if (props.visible && props.reportId) {
    fetchReportDetail();
  }
});
</script>

<style lang="scss" scoped>
.detail-container {
  padding: 20px;
}

.search-section {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);

  :deep(.el-form-item) {
    margin-bottom: 12px;
    margin-right: 16px;
  }

  :deep(.el-form-item__label) {
    font-size: 14px;
    color: var(--el-text-color-primary);
    font-weight: 500;
  }

  :deep(.el-input__wrapper) {
    background: var(--el-bg-color);
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      border-color: var(--el-border-color-hover);
    }

    &.is-focus {
      border-color: var(--el-color-primary);
    }
  }
}

.search-result {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;

  .el-tag {
    padding: 0 12px;
    height: 24px;
    line-height: 24px;
    font-size: 13px;
    border-radius: 12px;
  }
}

.info-section {
  margin-bottom: 24px;
  padding: 16px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.data-section {
  .section-title {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}

.section-title {
  margin-bottom: 12px;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 12px;
}

.info-item {
  display: flex;
  align-items: center;

  .label {
    font-size: 14px;
    color: var(--el-text-color-secondary);
    margin-right: 8px;
    min-width: 80px;
  }

  .value {
    font-size: 14px;
    color: var(--el-text-color-primary);
    font-weight: 500;
  }
}

.no-report {
  color: var(--el-text-color-placeholder);
  font-size: 12px;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--el-text-color-placeholder);
  font-size: 14px;

  .el-icon {
    font-size: 48px;
    margin-bottom: 12px;
  }
}

:deep(.el-table) {
  .el-table__header-wrapper {
    th {
      background-color: var(--el-bg-color-page) !important;
      font-weight: 600;
      color: var(--el-text-color-primary) !important;
    }
  }

  .el-table__body-wrapper {
    .el-table__row {
      td {
        font-size: 14px;
        color: var(--el-text-color-primary);
      }

      &:hover {
        td {
          background-color: var(--el-table-row-hover-bg-color) !important;
        }
      }
    }
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .detail-container {
    padding: 12px;
  }

  .search-section {
    padding: 12px;

    :deep(.el-form-item) {
      margin-bottom: 8px;
      margin-right: 12px;
    }

    :deep(.el-form-item__label) {
      font-size: 13px;
    }
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .info-item {
    .label {
      min-width: 70px;
      font-size: 13px;
    }

    .value {
      font-size: 13px;
    }
  }
}
</style>
