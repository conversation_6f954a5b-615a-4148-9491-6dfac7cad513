<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
        <el-form-item prop="kitsNo" label="证书编号" class="search-item">
          <el-input v-model="queryParams.kitsNo" placeholder="证书编号" clearable />
        </el-form-item>
        <el-form-item prop="organization" label="机构名称" class="search-item">
          <el-input v-model="queryParams.organization" placeholder="机构名称" clearable />
        </el-form-item>
        <el-form-item prop="petNo" label="宠物编号" class="search-item">
          <el-input v-model="queryParams.petNo" placeholder="宠物编号" clearable />
        </el-form-item>
        <el-form-item prop="status" label="状态" class="search-item">
          <!-- 0 未处理 1处理中 2处理完成 3 处理失败 -->
          <el-select v-model="queryParams.status" placeholder="状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="未处理" value="0" />
            <el-option label="处理中" value="1" />
            <el-option label="处理完成" value="2" />
            <el-option label="处理失败" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="hover" class="data-table">
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        class="data-table__content"
      >
        <el-table-column label="证书编号" prop="kitsNo">
          <template #default="scope">
            {{ scope.row.kitsNo }}
            <CopyButton :text="scope.row.kitsNo" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="机构名称" prop="organization" />
        <el-table-column label="宠物编号" prop="petNo">
          <template #default="scope">
            {{ scope.row.petNo }}
            <CopyButton :text="scope.row.petNo" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="宠物性别" prop="petSex" />
        <el-table-column label="宠物品种" prop="petBreed" />
        <el-table-column label="状态" prop="status">
          <template #default="scope">
            <el-tag :type="getReportStatusColor(scope.row.status)">
              {{ getReportStatus(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="上传时间" prop="createTime" />
        <el-table-column fixed="right" label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" size="small" link @click="handleDetail(scope.row)">
              明细
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="fetchData"
      />
    </el-card>

    <!-- 明细对话框 -->
    <auto-report-detail-dialog
      :visible="detailVisible"
      :report-id="currentReportId"
      @close="handleCloseDetail"
    />
  </div>
</template>

<script setup lang="ts">
import AutoReportAPI, { ReportListParams } from "@/api/autoreport";
import { ref, reactive, onMounted } from "vue";
import { useRoute } from "vue-router";
import AutoReportDetailDialog from "./components/AutoReportDetailDialog.vue";

defineOptions({
  name: "Order",
  inheritAttrs: false,
});

const route = useRoute();
const loading = ref(false);
const total = ref(0);
const pageData = ref<any[]>([]);

const queryParams = reactive<ReportListParams>({
  pageNum: 1,
  pageSize: 10,
  kitsNo: "",
  organization: "",
  petNo: "",
  status: "",
});

// 明细对话框相关
const detailVisible = ref(false);
const currentReportId = ref<number | string>("");

// 查询（重置页码后获取数据）
function handleQuery() {
  console.log(route.query.userId);
  queryParams.pageNum = 1;
  fetchData();
}

const fetchData = () => {
  loading.value = true;
  AutoReportAPI.getReportList(queryParams).then((res: any) => {
    pageData.value = res.records;
    total.value = res.total;
    loading.value = false;
  });
};

const queryFormRef = ref();
// 重置查询
function handleResetQuery() {
  queryFormRef.value.resetFields();
  queryParams.pageNum = 1;
  fetchData();
}

/**
 * 报告状态
 * @param status 0: 未处理, 1: 处理中, 2: 处理完成, 3: 处理失败
 */
const getReportStatus = (status: number) => {
  switch (status) {
    case 0:
      return "未处理";
    case 1:
      return "处理中";
    case 2:
      return "处理完成";
    case 3:
      return "处理失败";
    default:
      return "未知";
  }
};
/**
 * 报告状态颜色
 * @param status 0: 未处理, 1: 处理中, 2: 处理完成, 3: 处理失败
 */
const getReportStatusColor = (status: number) => {
  switch (status) {
    case 0:
      return "info";
    case 1:
      return "warning";
    case 2:
      return "success";
    case 3:
      return "danger";
  }
};

// 查看明细
const handleDetail = (row: any) => {
  currentReportId.value = row.id;
  detailVisible.value = true;
};

// 关闭明细对话框
const handleCloseDetail = () => {
  detailVisible.value = false;
  currentReportId.value = "";
};

onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
.search-item {
  width: 200px;
}
</style>
