<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
        <el-form-item prop="userId" label="用户" style="width: 400px">
          <el-select
            v-model="queryParams.userId"
            placeholder="请选择用户"
            clearable
            filterable
            :loading="userLoading"
          >
            <el-option
              v-for="item in userList"
              :key="item.id"
              :label="item.nickName"
              :value="item.id"
            >
              <div style="display: flex; align-items: center">
                <el-avatar :size="24" :src="getImageUrl(item.avatar)" style="margin-right: 8px" />
                <span>{{ item.nickName + "-" + item.openId }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="orderNo" label="订单号" style="width: 300px">
          <el-input
            v-model="queryParams.orderNo"
            placeholder="订单号"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="payStatus" label="支付状态" class="search-item">
          <el-select v-model="queryParams.payStatus" placeholder="支付状态" clearable>
            <el-option label="待支付" value="0" />
            <el-option label="已支付" value="1" />
            <el-option label="已取消" value="2" />
          </el-select>
        </el-form-item>
        <el-form-item prop="orderStatus" label="订单状态" class="search-item">
          <el-select v-model="queryParams.orderStatus" placeholder="订单状态" clearable>
            <el-option label="待支付" value="0" />
            <el-option label="已支付" value="1" />
            <el-option label="部分使用" value="2" />
            <el-option label="已使用" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item prop="logistics" label="物流" style="width: 300px">
          <el-input v-model="queryParams.logistics" placeholder="物流" clearable />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="hover" class="data-table">
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        class="data-table__content"
      >
        <el-table-column label="订单号" prop="orderNo" min-width="240">
          <template #default="{ row }">
            <span style="margin-right: 10px">{{ row.orderNo }}</span>
            <CopyButton :text="row.orderNo" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="订单备注" prop="remark" width="200">
          <template #default="{ row }">
            <el-tooltip v-if="row.remark.length > 10" :content="row.remark" placement="top">
              <span>{{ row.remark.slice(0, 10) + "..." }}</span>
            </el-tooltip>
            <span v-else>{{ row.remark || "无" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="支付状态" prop="payStatus" width="150">
          <template #default="scope">
            <el-tag :type="getPayStatusColor(scope.row.payStatus)">
              {{ getPayStatus(scope.row.payStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" prop="orderStatus" width="150">
          <template #default="scope">
            <el-tag :type="getOrderStatusColor(scope.row.orderStatus)">
              {{ getOrderStatus(scope.row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="发货物流单号" prop="logistics" width="220">
          <template #default="scope">
            <OrderLogisticsPopover :logistics-no="scope.row.logistics">
              <el-button type="primary" text size="small">
                {{ scope.row.logistics }}
              </el-button>
            </OrderLogisticsPopover>

            <CopyButton v-if="scope.row.logistics" :text="scope.row.logistics" size="small" />
          </template>
        </el-table-column>
        <el-table-column label="订单原价" prop="orderSourceAmount" width="100" />
        <el-table-column label="订单金额" prop="orderAmount" width="100" />
        <el-table-column label="支付金额" prop="payAmount" width="100" />
        <el-table-column label="支付时间" prop="payTime" width="200" />
        <el-table-column label="创建时间" prop="createTime" width="200" />
        <el-table-column fixed="right" label="操作" width="220">
          <template #default="scope">
            <el-button type="primary" size="small" link @click="openAddressDrawer(scope.row)">
              收件地址
            </el-button>
            <el-button type="primary" size="small" link @click="openDetailDrawer(scope.row)">
              明细
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="fetchData"
      />
    </el-card>

    <!-- 地址抽屉组件 -->
    <OrderAddressDrawer
      :visible="addressDrawerVisible"
      :currentAddress="currentAddress"
      @close="addressDrawerVisible = false"
    />

    <!-- 详情抽屉组件 -->
    <OrderDetailDrawer
      :visible="detailDrawerVisible"
      :order-no="orderNo"
      @close="detailDrawerVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import OrderAPI, { OrderListParams } from "@/api/order";
import UserAPI from "@/api/user";
import { ref, reactive, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import OrderAddressDrawer from "@/views/order/components/OrderAddressDrawer.vue";
import OrderDetailDrawer from "@/views/order/components/OrderDetailDrawer.vue";
import OrderLogisticsPopover from "@/views/order/components/OrderLogisticsPopover.vue";
import { getImageUrl } from "@/utils/image";

defineOptions({
  name: "Order",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const total = ref(0);
const pageData = ref<any[]>([]);

const queryParams = reactive<OrderListParams>({
  pageNum: 1,
  pageSize: 10,
  userId: route.query.userId ? Number(route.query.userId) : undefined,
});

const userLoading = ref(false);
const userList = ref<any[]>([]);

// 获取用户列表
const fetchUserList = async () => {
  if (userList.value.length > 0) return;
  userLoading.value = true;
  try {
    const res = await UserAPI.getUserList();
    userList.value = res;
  } finally {
    userLoading.value = false;
  }
};

// 查询（重置页码后获取数据）
function handleQuery() {
  console.log(route.query.userId);
  queryParams.pageNum = 1;
  fetchData();
}

const fetchData = () => {
  loading.value = true;
  OrderAPI.getOrderList(queryParams).then((res: any) => {
    pageData.value = res.records;
    total.value = res.total;
    loading.value = false;
  });
};

const queryFormRef = ref();
// 重置查询
function handleResetQuery() {
  if (queryParams.userId) {
    router.push({
      path: "/order/index",
    });
    queryFormRef.value.resetFields();
    queryParams.pageNum = 1;
    queryParams.userId = undefined;
    fetchData();
  } else {
    queryFormRef.value.resetFields();
    queryParams.pageNum = 1;
    fetchData();
  }
}

/**
 * 订单状态
 * @param status 0: 待支付, 1: 已支付, 2: 部分使用, 3: 已使用
 */
const getOrderStatus = (status: number) => {
  switch (status) {
    case 0:
      return "待支付";
    case 1:
      return "已支付";
    case 2:
      return "部分使用";
    case 3:
      return "已使用";
    default:
      return "未知";
  }
};
/**
 * 订单状态颜色
 * @param status 0: 待支付, 1: 已支付, 2: 部分使用, 3: 已使用
 */
const getOrderStatusColor = (status: number) => {
  switch (status) {
    case 0:
      return "danger";
    case 1:
      return "success";
    case 2:
      return "warning";
    case 3:
      return "info";
  }
};

/**
 * 支付状态
 * @param status 0 待支付 1已支付 2部分使用 3已使用
 */
const getPayStatus = (status: number) => {
  switch (status) {
    case 0:
      return "待支付";
    case 1:
      return "已支付";
    case 2:
      return "已取消";
    default:
      return "未知";
  }
};
/**
 * 支付状态颜色
 * @param status 0 待支付 1已支付 2部分使用 3已使用
 */
const getPayStatusColor = (status: number) => {
  switch (status) {
    case 0:
      return "danger";
    case 1:
      return "success";
    case 2:
      return "info";
    default:
      return "info";
  }
};

const addressDrawerVisible = ref(false);
const currentAddress = ref<any>({});
function openAddressDrawer(row: any) {
  currentAddress.value = {
    name: row.name,
    phone: row.mobile,
    province: row.province,
    city: row.city,
    district: row.district,
    address: row.address,
  };
  addressDrawerVisible.value = true;
}

const detailDrawerVisible = ref(false);
const orderNo = ref<string>("");
function openDetailDrawer(row: any) {
  // 假设 row.details 是订单详情数组，如无可自行调整
  orderNo.value = row.orderNo;

  detailDrawerVisible.value = true;
}

onMounted(() => {
  fetchData();
  fetchUserList();
});
</script>

<style lang="scss" scoped>
.search-item {
  width: 200px;
}
</style>
