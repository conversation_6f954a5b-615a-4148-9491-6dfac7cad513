<template>
  <el-dialog
    :model-value="visible"
    title="收货地址"
    width="500px"
    top="10vh"
    :close-on-click-modal="false"
    @close="$emit('close')"
  >
    <div v-if="currentAddress" class="address-simple-card">
      <div class="simple-row">
        <span class="simple-label">收件人</span>
        <span class="simple-value">{{ currentAddress.name || "-" }}</span>
      </div>
      <div class="simple-row">
        <span class="simple-label">手机号</span>
        <span class="simple-value">{{ currentAddress.phone || "-" }}</span>
      </div>
      <div class="simple-row">
        <span class="simple-label">省市区</span>
        <span class="simple-value">{{ region || "-" }}</span>
      </div>
      <div class="simple-row detail-row">
        <span class="simple-label">详细地址</span>
        <div class="simple-detail-value">{{ currentAddress.address || "-" }}</div>
      </div>
    </div>
    <div v-else class="empty">暂无地址信息</div>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  visible: boolean;
  currentAddress: {
    name?: string;
    phone?: string;
    province?: string;
    city?: string;
    district?: string;
    address?: string;
  };
}>();

defineEmits(["close"]);

const region = computed(() => {
  if (!props.currentAddress) return "";
  const { province = "", city = "", district = "" } = props.currentAddress;
  return [province, city, district].filter(Boolean).join(" ");
});
</script>

<style scoped>
.address-simple-card {
  background: var(--el-bg-color);
  border-radius: 12px;
  padding: 36px 28px 28px 28px;
  margin: 0 0 10px 0;
  box-shadow: var(--el-box-shadow-light);
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.simple-row {
  display: flex;
  align-items: flex-start;
  gap: 0;
  margin-bottom: 0;
}
.simple-label {
  color: var(--el-text-color-primary);
  font-weight: 500;
  min-width: 72px;
  font-size: 16px;
  letter-spacing: 0.5px;
  flex-shrink: 0;
  line-height: 2;
}
.simple-value {
  color: var(--el-text-color-regular);
  font-size: 16px;
  margin-left: 18px;
  word-break: break-all;
  flex: 1;
  line-height: 2;
}
.detail-row {
  flex-direction: column;
  align-items: flex-start;
  margin-top: 8px;
}
.detail-row .simple-label {
  margin-bottom: 6px;
}
.simple-detail-value {
  background: var(--el-bg-color-page);
  border-radius: 7px;
  padding: 12px 16px;
  font-size: 15px;
  color: var(--el-text-color-primary);
  width: 100%;
  box-sizing: border-box;
  box-shadow: var(--el-box-shadow-lighter);
  line-height: 1.8;
}
.empty {
  color: var(--el-text-color-placeholder);
  text-align: center;
  font-size: 16px;
  margin-top: 40px;
}
</style>
