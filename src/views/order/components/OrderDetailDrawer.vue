<template>
  <el-drawer
    :model-value="visible"
    :title="`订单详情`"
    direction="rtl"
    size="1300px"
    @close="$emit('close')"
  >
    <template #header>
      <div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
        <span>订单详情</span>

        <el-button type="primary" text @click="fetchOrderDetails" :loading="loading">
          刷新
        </el-button>
      </div>
    </template>
    <div v-if="orderDetails?.length" class="order-detail-list">
      <el-table :data="orderDetails" border stripe style="width: 100%">
        <el-table-column prop="title" label="套餐名" width="200" />
        <el-table-column prop="image" label="套餐图" width="100">
          <template #default="{ row }">
            <el-image :src="getImageUrl(row.image)" />
          </template>
        </el-table-column>
        <el-table-column prop="goodsSpecsName" label="购买规格" align="center" width="500">
          <template #default="{ row }">
            <div v-if="row.goodsSpecsClassifyMap" style="margin-top: 8px; text-align: left">
              <div
                v-for="(specs, classify) in parseClassifyMap(row.goodsSpecsClassifyMap)"
                :key="classify"
                style="margin-bottom: 4px"
              >
                <span style="font-weight: bold; margin-right: 8px">{{ classify }}:</span>
                <el-tag
                  v-for="spec in specs"
                  :key="spec"
                  type="success"
                  size="small"
                  style="
                    margin-right: 5px;
                    max-width: 200px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    display: inline-flex;
                    align-items: center;
                    vertical-align: middle;
                  "
                >
                  <el-tooltip v-if="spec.length > 10" :content="spec" placement="top">
                    <span style="display: inline-flex; align-items: center; height: 100%">
                      {{ spec.slice(0, 10) + "…" }}
                    </span>
                  </el-tooltip>
                  <template v-else>
                    <span style="display: inline-flex; align-items: center; height: 100%">
                      {{ spec }}
                    </span>
                  </template>
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="number" label="下单数量" align="center" width="100" />
        <el-table-column prop="stock" label="已用数量" align="center" width="100">
          <template #default="{ row }">
            {{ row.number - row.stock }}
          </template>
        </el-table-column>
        <el-table-column prop="stock" label="可用数量" align="center" width="100" />
        <el-table-column fixed="right" label="操作" width="220" show-overflow-tooltip>
          <template #default="{ row }">
            <el-button type="primary" size="small" link @click="openBindPetDialog(row)">
              已绑列表
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-else class="empty">暂无订单详情</div>
    <OrderBindPetDialog
      :title="`已绑宠物列表-${currentOrderDetail.title}`"
      :visible="showBindPetDialog"
      :goods-type="currentOrderDetail.goodsType"
      :order-detail-id="currentOrderDetail.id"
      @close="showBindPetDialog = false"
    />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { Refresh } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import OrderAPI from "@/api/order";
import { getImageUrl } from "@/utils/image";
import OrderBindPetDialog from "./OrderBindPetDialog.vue";

const props = defineProps<{
  visible: boolean;
  orderNo: string;
}>();

defineEmits(["close"]);

const orderDetails = ref<any[]>([]);
const loading = ref(false);
const showBindPetDialog = ref(false);
const currentOrderDetail = ref<any>({});
const fetchOrderDetails = async () => {
  if (!props.orderNo) {
    orderDetails.value = [];
    return;
  }
  loading.value = true;
  try {
    orderDetails.value = await OrderAPI.getOrderDetails(props.orderNo);
  } catch (error) {
    ElMessage.error("获取订单详情失败");
    console.error("获取订单详情失败:", error);
  } finally {
    loading.value = false;
  }
};

watch(
  () => [props.orderNo, props.visible],
  () => {
    if (props.visible && props.orderNo) {
      fetchOrderDetails();
    }
  }
);

function parseClassifyMap(map: string | Record<string, string[]>) {
  // 兼容字符串和对象
  if (typeof map === "string") {
    try {
      return JSON.parse(map);
    } catch {
      return {};
    }
  }
  return map || {};
}

function openBindPetDialog(orderDetail: any) {
  currentOrderDetail.value = orderDetail;
  showBindPetDialog.value = true;
}
</script>

<style scoped>
.order-detail-list {
  margin-top: 10px;
}

.loading {
  color: #888;
  text-align: center;
  font-size: 16px;
  margin-top: 40px;
}

.empty {
  color: #bbb;
  text-align: center;
  font-size: 16px;
  margin-top: 40px;
}

.el-drawer__header {
  margin-bottom: 0;
  padding: 16px 20px;
}
</style>
