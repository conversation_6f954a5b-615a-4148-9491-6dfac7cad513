<template>
  <el-dialog
    :model-value="visible"
    width="1200px"
    top="5vh"
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    custom-class="order-report-dialog-mobile"
    @close="$emit('close')"
  >
    <div class="report-container" v-loading="loading">
      <div class="report-title">检测报告</div>
      <div class="report-section">
        <div class="report-row">
          <div class="report-col">
            <div class="report-label">繁育机构：</div>
            <div class="report-value">{{ reportData.breederName }}</div>
          </div>
          <div class="report-col">
            <div class="report-label">性别：</div>
            <div class="report-value">{{ reportData.genderName }}</div>
          </div>
        </div>
        <div class="report-row">
          <div class="report-col">
            <div class="report-label">宠物名称：</div>
            <div class="report-value">{{ reportData.petName }}</div>
          </div>
          <div class="report-col">
            <div class="report-label">宠物品种：</div>
            <div class="report-value">{{ reportData.breedName }}</div>
          </div>
        </div>
      </div>
      <div class="report-title" style="margin-top: 24px">检测数据</div>

      <div class="table-container">
        <el-table
          :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
          :data="filteredTableData"
          border
          style="width: 100%"
          header-cell-class-name="report-table-header"
          height="600"
          :max-height="600"
        >
          <el-table-column prop="group" label="分类" min-width="120" show-overflow-tooltip>
            <template #header>
              <div class="table-header-cell">
                <div class="header-content">
                  <span class="header-title">分类</span>
                  <div class="header-search">
                    <el-select
                      v-model="filterForm.group"
                      placeholder="选择分类"
                      clearable
                      size="small"
                      @change="handleFilter"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="option in groupOptions"
                        :key="option"
                        :label="option"
                        :value="option"
                      />
                    </el-select>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="projectName" label="项目" min-width="120" show-overflow-tooltip>
            <template #header>
              <div class="table-header-cell">
                <div class="header-content">
                  <span class="header-title">项目</span>
                  <div class="header-search">
                    <el-input
                      v-model="filterForm.projectName"
                      placeholder="搜索项目"
                      clearable
                      size="small"
                      @input="handleFilter"
                    >
                      <template #prefix>
                        <el-icon class="search-icon"><Search /></el-icon>
                      </template>
                    </el-input>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="locusName" label="位点" min-width="120" show-overflow-tooltip>
            <template #header>
              <div class="table-header-cell">
                <div class="header-content">
                  <span class="header-title">位点</span>
                  <div class="header-search">
                    <el-input
                      v-model="filterForm.locusName"
                      placeholder="搜索位点"
                      clearable
                      size="small"
                      @input="handleFilter"
                    >
                      <template #prefix>
                        <el-icon class="search-icon"><Search /></el-icon>
                      </template>
                    </el-input>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="result" label="基因型" min-width="120" show-overflow-tooltip />
          <el-table-column prop="coat" label="毛色表现" min-width="120" show-overflow-tooltip />
          <el-table-column
            prop="resultInterpretation"
            label="结果解读"
            min-width="120"
            show-overflow-tooltip
          />
        </el-table>
        <div
          class="table-footer"
          v-if="filterForm.group || filterForm.projectName || filterForm.locusName"
        >
          <el-tag type="info" effect="plain" size="small">
            已筛选 {{ filteredTableData.length }} 条数据
          </el-tag>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watchEffect, computed } from "vue";
import { Search } from "@element-plus/icons-vue";
import OrderAPI from "@/api/order";

const props = defineProps<{
  visible: boolean;
  id: string | number;
}>();
const loading = ref(false);
const reportData = ref<any>({
  orgName: "",
  reportNo: "",
  petName: "",
  petRuName: "",
  varietiesName: "",
  genderName: "",
  projectName: "",
  items: [],
});

// 筛选表单
const filterForm = ref({
  group: "",
  projectName: "",
  locusName: "",
});

// 原始数据
const originalTableData = ref<any[]>([]);

// 获取所有分类选项
const groupOptions = computed(() => {
  const groups = [...new Set(originalTableData.value.map((item) => item.group).filter(Boolean))];
  return groups.sort((a, b) => a.localeCompare(b, "zh-CN"));
});

// 过滤后的数据
const filteredTableData = computed(() => {
  let result = [...originalTableData.value];

  if (filterForm.value.group) {
    result = result.filter((item) => item.group === filterForm.value.group);
  }

  if (filterForm.value.projectName) {
    result = result.filter((item) =>
      item.projectName.toLowerCase().includes(filterForm.value.projectName.toLowerCase())
    );
  }

  if (filterForm.value.locusName) {
    result = result.filter((item) =>
      item.locusName.toLowerCase().includes(filterForm.value.locusName.toLowerCase())
    );
  }

  // 按照分类排序
  result.sort((a, b) => {
    if (a.group && b.group) {
      return a.group.localeCompare(b.group, "zh-CN");
    }
    return 0;
  });

  return result;
});

// 处理筛选
function handleFilter() {
  // 可以在这里添加防抖逻辑
}

// mock 获取报告详情
async function fetchReport() {
  console.log("fetchReport", props.id);
  if (!props.id) return;
  loading.value = true;
  try {
    // TODO: 替换为实际API
    const res = await OrderAPI.getReport(props.id);
    console.log(res);
    reportData.value = res;
    originalTableData.value = res.items || [];
  } finally {
    loading.value = false;
  }
}

watchEffect(() => {
  if (props.visible && props.id) {
    fetchReport();
  }
});
</script>

<style lang="scss" scoped>
.order-report-dialog-mobile {
  min-width: 320px;
  max-width: 400px;
  padding: 0;
}

.report-container {
  padding: 16px 8px 24px 8px;
  font-size: 15px;
  color: var(--el-text-color-primary);
  background: var(--el-bg-color);
  border-radius: 8px;
}

.report-title {
  margin-top: 8px;
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.report-section {
  padding: 12px 8px 4px 8px;
  margin-bottom: 8px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
}

.report-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.report-col {
  flex: 1 1 0;
  min-width: 120px;
  margin-bottom: 4px;
}

.report-label {
  margin-bottom: 2px;
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

.report-value {
  min-width: 60px;
  font-size: 15px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  word-break: break-all;
}

.report-table-header {
  font-size: 15px;
  font-weight: bold;
  color: var(--el-text-color-primary) !important;
  background: var(--el-bg-color-page) !important;
}

.table-container {
  margin-top: 0;

  :deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--el-bg-color);
    color: var(--el-text-color-primary);

    .el-table__header-wrapper {
      th {
        background-color: var(--el-bg-color-page) !important;
        font-weight: 600;
        color: var(--el-text-color-primary) !important;
        height: auto;
        padding: 8px;
      }
    }

    .el-table__body-wrapper {
      .el-table__row {
        td {
          font-size: 14px;
          background-color: var(--el-bg-color);
          color: var(--el-text-color-primary);
        }

        &:hover {
          td {
            background-color: var(--el-table-row-hover-bg-color) !important;
          }
        }
      }
    }
  }
}

.table-header-cell {
  padding: 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 4px;
}

.header-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  white-space: nowrap;
  min-width: 36px;
}

.header-search {
  flex: 1;
  min-width: 0;

  :deep(.el-input__wrapper) {
    padding: 0 8px;
    height: 28px;
    background: var(--el-bg-color);
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
    transition: all 0.2s;

    &:hover {
      box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
  }

  :deep(.el-input__inner) {
    height: 28px;
    font-size: 12px;
    color: var(--el-text-color-primary);

    &::placeholder {
      color: var(--el-text-color-placeholder);
    }
  }

  :deep(.el-select) {
    .el-input__wrapper {
      padding: 0 8px;
      height: 28px;
      background: var(--el-bg-color);
      box-shadow: 0 0 0 1px var(--el-border-color) inset;
      transition: all 0.2s;

      &:hover {
        box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
      }

      &.is-focus {
        box-shadow: 0 0 0 1px var(--el-color-primary) inset;
      }
    }

    .el-input__inner {
      height: 28px;
      font-size: 12px;
      color: var(--el-text-color-primary);

      &::placeholder {
        color: var(--el-text-color-placeholder);
      }
    }
  }
}

.search-icon {
  font-size: 14px;
  color: var(--el-text-color-placeholder);
  margin-right: 4px;
}

.filter-tag {
  align-self: flex-start;
  padding: 0 8px;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  border-radius: 10px;
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
  border: 1px solid var(--el-color-primary-light-5);
}

.table-footer {
  display: flex;
  justify-content: flex-end;
  padding: 8px 0;

  .el-tag {
    padding: 0 12px;
    height: 24px;
    line-height: 24px;
    font-size: 13px;
    border-radius: 12px;
  }
}

// 移动端适配
@media screen and (max-width: 768px) {
  .table-container {
    :deep(.el-table) {
      .el-table__header-wrapper {
        th {
          padding: 6px 4px;
        }
      }
    }
  }

  .header-content {
    gap: 6px;
    padding: 0 2px;
  }

  .header-title {
    font-size: 13px;
    min-width: 32px;
  }

  .header-search {
    :deep(.el-input__wrapper) {
      height: 26px;
    }

    :deep(.el-input__inner) {
      height: 26px;
      font-size: 12px;
    }

    :deep(.el-select) {
      .el-input__wrapper {
        height: 26px;
      }

      .el-input__inner {
        height: 26px;
        font-size: 12px;
      }
    }
  }

  .search-icon {
    font-size: 12px;
  }
}
</style>
