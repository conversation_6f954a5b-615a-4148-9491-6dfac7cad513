<template>
  <el-popover
    placement="bottom"
    width="500"
    trigger="click"
    @show="handleShow"
    ref="popoverRef"
    teleported
  >
    <template #reference>
      <slot>
        <el-button type="primary" size="small" link>查看物流</el-button>
      </slot>
    </template>
    <div v-if="logisticsNo" style="margin-bottom: 8px; font-weight: bold">
      物流单号：{{ logisticsNo }}
    </div>
    <el-skeleton v-if="loading" animated :rows="4" style="margin: 20px 0" />
    <div v-else-if="logisticsResponse.hasRouters" class="logistics-timeline-container">
      <el-timeline style="margin: 20px 20px">
        <el-timeline-item
          v-for="(item, idx) in logisticsResponse.routers"
          :key="idx"
          :timestamp="item.acceptTime"
          :color="idx === 0 ? '#67C23A' : ''"
        >
          {{ item.remark }}
        </el-timeline-item>
      </el-timeline>
    </div>
    <div
      v-else-if="!logisticsResponse.hasRouters"
      style="text-align: center; color: #bbb; margin: 20px 0"
    >
      暂无物流信息
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, defineExpose } from "vue";
import OrderAPI from "@/api/order";

const props = defineProps<{
  logisticsNo: string;
}>();

const visible = ref(false);
const logisticsResponse = ref<any>({});
const popoverRef = ref();
const loading = ref(false);

async function fetchLogistics() {
  if (!props.logisticsNo) {
    logisticsResponse.value = {
      hasLogistics: false,
    };
    return;
  }
  loading.value = true;
  try {
    const res = await OrderAPI.getLogistics(props.logisticsNo);
    logisticsResponse.value = res;
  } finally {
    loading.value = false;
  }
}

function handleShow() {
  visible.value = true;
  fetchLogistics();
}

function open() {
  visible.value = true;
  fetchLogistics();
}

function close() {
  visible.value = false;
}

defineExpose({ open, close, popoverRef });
</script>

<style scoped>
.logistics-timeline-container {
  height: 400px;
  overflow-y: auto;
  padding-right: 8px;
}

.logistics-timeline-container::-webkit-scrollbar {
  width: 6px;
}

.logistics-timeline-container::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.logistics-timeline-container::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}
</style>
