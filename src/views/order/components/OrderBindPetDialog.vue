<template>
  <el-dialog
    :model-value="visible"
    width="1300px"
    :close-on-click-modal="false"
    @close="$emit('close')"
  >
    <template #header>
      <div style="display: flex; align-items: center; justify-content: space-between; width: 100%">
        <span>{{ title }}</span>
        <el-button type="primary" text @click="fetchPetList" :loading="loading">刷新</el-button>
      </div>
    </template>
    <el-table :data="petList" border stripe style="width: 100%" v-loading="loading">
      <el-table-column prop="kitsNo" label="试剂盒编号" width="180" fixed="left">
        <template #default="{ row }">
          {{ row.kitsNo }}
          <CopyButton :text="row.kitsNo" size="small" />
        </template>
      </el-table-column>
      <el-table-column prop="logistics" label="回寄物流单号" width="220">
        <template #default="{ row }">
          <OrderLogisticsPopover :logistics-no="row.logistics">
            <el-button type="primary" text size="small">
              {{ row.logistics }}
            </el-button>
          </OrderLogisticsPopover>

          <CopyButton v-if="row.logistics" :text="row.logistics" size="small" />
        </template>
      </el-table-column>
      <el-table-column prop="avatar" label="头像" width="100" align="center">
        <template #default="{ row }">
          <el-avatar :size="30" :src="getImageUrl(row.avatar)" />
        </template>
      </el-table-column>
      <el-table-column prop="petName" label="宠物名" width="100" />
      <el-table-column prop="detectStatus" label="状态" width="100">
        <template #default="{ row }">
          <!--  0待检测 1待收件 2检测中 3已检测 4已绑定 -->
          <el-tag :type="getStatusType(row.detectStatus)">
            {{ getStatusText(row.detectStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="genderName" label="性别" width="100" />
      <el-table-column prop="varietiesName" label="宠物品种" width="150">
        <template #default="{ row }">
          {{ row.varietiesName || "无" }}
        </template>
      </el-table-column>
      <el-table-column prop="orderVarietiesName" label="套餐品种" width="300">
        <template #default="{ row }">
          <template v-if="props.goodsType === 2">
            <div
              v-if="editingRow?.id === row.id"
              style="display: flex; align-items: center; gap: 8px"
            >
              <el-select
                v-model="editingRow.orderVarietiesName"
                placeholder="请选择品种"
                style="width: 200px"
                :loading="varietiesLoading"
                filterable
                clearable
                :filter-method="filterVarieties"
              >
                <el-option
                  v-for="item in filteredVarietiesList"
                  :key="item.title"
                  :label="item.title"
                  :value="item.title"
                />
              </el-select>
              <el-button type="primary" size="small" link @click="handleSaveVarieties(row)">
                确认
              </el-button>
              <el-button size="small" type="danger" link @click="cancelEdit">取消</el-button>
            </div>
            <div v-else style="display: flex; align-items: center; gap: 8px">
              <span>{{ row.orderVarietiesName || "无" }}</span>
              <el-button type="primary" link @click="handleEditVarieties(row)">编辑</el-button>
            </div>
          </template>
          <span v-else>非品种套餐</span>
        </template>
      </el-table-column>
      <el-table-column prop="isReport" label="报告" width="200" fixed="right">
        <template #default="{ row }">
          <el-tag v-if="!row.isReport" type="danger">未生成</el-tag>
          <el-button
            v-if="row.isReport"
            type="primary"
            text
            size="small"
            @click="previewReport(row.petId)"
          >
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 16px; text-align: right">
      <el-pagination
        background
        layout="prev, pager, next, jumper, ->, total"
        :total="total"
        :page-size="pageSize"
        :current-page="pageNum"
        @current-change="handlePageChange"
      />
    </div>
    <el-image-viewer
      v-if="showCertificateViewer"
      :url-list="certificateUrl"
      @close="showCertificateViewer = false"
    />
    <order-report-dialog
      v-if="showReportViewer"
      :visible="showReportViewer"
      :id="petId"
      @close="showReportViewer = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import OrderAPI from "@/api/order";
import { getImageUrl, getCertImageUrl } from "@/utils/image";
import { ElImageViewer, ElMessage } from "element-plus";
import OrderReportDialog from "./OrderReportDialog.vue";

const props = defineProps<{
  visible: boolean;
  title: string;
  orderDetailId: string | number;
  goodsType: string | number;
}>();

defineEmits(["close"]);

const petList = ref<any[]>([]);
const loading = ref(false);
const total = ref(0);
const pageNum = ref(1);
const pageSize = ref(10);
const showCertificateViewer = ref(false);
const certificateUrl = ref<string[]>([]);

// 品种相关
const varietiesList = ref<any[]>([]);
const filteredVarietiesList = ref<any[]>([]);
const varietiesLoading = ref(false);
const editingRow = ref<any>(null);

async function fetchPetList() {
  if (!props.orderDetailId) return;
  loading.value = true;
  try {
    const res = await OrderAPI.getBinList({
      orderDetailId: Number(props.orderDetailId),
      pageNum: pageNum.value,
      pageSize: pageSize.value,
    });
    petList.value = res.records;
    total.value = res.total;
  } finally {
    loading.value = false;
  }
}

function handlePageChange(page: number) {
  pageNum.value = page;
  fetchPetList();
}

function copy(text: string) {
  if (!text) return;
  navigator.clipboard.writeText(text);
  ElMessage.success("复制成功");
}

const showReportViewer = ref(false);
const petId = ref<string | number>("");

function previewCertificate(url: string) {
  certificateUrl.value = url.split(",").map((item) => getCertImageUrl(item));
  showCertificateViewer.value = true;
}

function previewReport(id: string | number) {
  console.log("查看报告", id);
  petId.value = id;
  showReportViewer.value = true;
}

/**
 * 收货成功
 */
async function pickUp(id: string | number) {
  console.log("收货成功", id);
  loading.value = true;
  try {
    const res = await OrderAPI.pickUp(id);
    if (res) {
      ElMessage.success("收货成功");
      fetchPetList();
    }
  } finally {
    loading.value = false;
  }
}

watch(
  () => [props.visible, props.orderDetailId],
  ([visible, id]) => {
    if (visible && id) {
      pageNum.value = 1;
      fetchPetList();
    }
  }
);
/**
 * 获取状态类型
 * @param status  0待检测 1待收件 2检测中 3已检测 4已绑定
 * @returns
 */
function getStatusType(status: number) {
  console.log(status);
  switch (status) {
    case 0:
      return "danger";
    case 1:
      return "warning";
    case 2:
      return "primary";
    case 3:
      return "info";
    case 4:
      return "success";
    default:
      return "info";
  }
}

/**
 * 获取状态文本
 * @param status  0待检测 1待收件 2检测中 3已检测 4已绑定
 * @returns
 */
function getStatusText(status: number) {
  switch (status) {
    case 0:
      return "待检测";
    case 1:
      return "待收件";
    case 2:
      return "检测中";
    case 3:
      return "已检测";
    case 4:
      return "已绑定";
    default:
      return "未知";
  }
}

// 获取品种列表
async function fetchVarietiesList() {
  if (varietiesList.value.length > 0) return;
  varietiesLoading.value = true;
  try {
    const res = await OrderAPI.getVarietiesList();
    varietiesList.value = res;
    filteredVarietiesList.value = res;
  } catch (error) {
    ElMessage.error("获取品种列表失败");
    console.error("获取品种列表失败:", error);
  } finally {
    varietiesLoading.value = false;
  }
}

// 搜索过滤品种
function filterVarieties(query: string) {
  if (query) {
    filteredVarietiesList.value = varietiesList.value.filter((item) =>
      item.title.toLowerCase().includes(query.toLowerCase())
    );
  } else {
    filteredVarietiesList.value = varietiesList.value;
  }
}

// 开始编辑
function handleEditVarieties(row: any) {
  editingRow.value = {
    id: row.id,
    orderVarietiesName: row.orderVarietiesName,
  };
  fetchVarietiesList();
}

// 取消编辑
function cancelEdit() {
  editingRow.value = null;
}

// 保存品种
async function handleSaveVarieties(row: any) {
  if (!editingRow.value?.orderVarietiesName) {
    ElMessage.warning("请选择品种");
    return;
  }

  try {
    const res = await OrderAPI.updatePetVarieties({
      id: row.id,
      varieties: editingRow.value.orderVarietiesName,
    });

    if (res) {
      ElMessage.success("更新成功");
      await fetchPetList();
      cancelEdit();
      varietiesLoading.value = false;
    }
  } catch (error) {
    ElMessage.error("更新失败");
    console.error("更新品种失败:", error);
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-table) {
  .el-table__fixed-body-wrapper {
    border: 0;
  }
}

.el-select {
  width: 200px;
}
</style>
