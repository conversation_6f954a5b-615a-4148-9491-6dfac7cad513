<template>
  <div class="dashboard-container">
    <!-- 图表区域 -->
    <el-row :gutter="16" class="mt-2">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">近 7 日每个商品总销量</div>
          </template>
          <div ref="chart1" style="width: 100%; height: 260px"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">近 7 日总支付订单数</div>
          </template>
          <div ref="chart2" style="width: 100%; height: 260px"></div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="16" class="mt-2">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">近 7 日每日注册新用户数</div>
          </template>
          <div ref="chart3" style="width: 100%; height: 260px"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">热门商品 TOP3</div>
          </template>
          <div ref="chart4" style="width: 100%; height: 260px"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as echarts from "echarts";

defineOptions({ name: "Dashboard", inheritAttrs: false });

const chart1 = ref<HTMLElement | null>(null);
const chart2 = ref<HTMLElement | null>(null);
const chart3 = ref<HTMLElement | null>(null);
const chart4 = ref<HTMLElement | null>(null);
let myChart1: echarts.ECharts | null = null;
let myChart2: echarts.ECharts | null = null;
let myChart3: echarts.ECharts | null = null;
let myChart4: echarts.ECharts | null = null;

// 模拟数据：近 7 日每个商品总销量（示例数据）
const productNames = ["商品A", "商品B", "商品C", "商品D", "商品E"];
const productSales = [120, 200, 150, 80, 70];

// 模拟数据：近 7 日总支付订单数（示例数据）
const orderDates = [
  "2023-10-01",
  "2023-10-02",
  "2023-10-03",
  "2023-10-04",
  "2023-10-05",
  "2023-10-06",
  "2023-10-07",
];
const orderCounts = [10, 52, 200, 334, 390, 330, 220];

// 模拟数据：近 7 日每日注册新用户数（示例数据）
const userDates = [
  "2023-10-01",
  "2023-10-02",
  "2023-10-03",
  "2023-10-04",
  "2023-10-05",
  "2023-10-06",
  "2023-10-07",
];
const userCounts = [120, 132, 101, 134, 90, 230, 210];

// 模拟数据：卖得最好的 top3 商品信息（示例数据）
const topProducts = [
  { name: "商品A", value: 120 },
  { name: "商品B", value: 200 },
  { name: "商品C", value: 150 },
];

function initChart1() {
  if (chart1.value) {
    myChart1 = echarts.init(chart1.value);
    const option = {
      title: { text: "近 7 日每个商品总销量", left: "center" },
      tooltip: { trigger: "axis" },
      xAxis: { type: "category", data: productNames },
      yAxis: { type: "value" },
      series: [{ name: "销量", type: "bar", data: productSales }],
    };
    myChart1.setOption(option);
  }
}

function initChart2() {
  if (chart2.value) {
    myChart2 = echarts.init(chart2.value);
    const option = {
      title: { text: "近 7 日总支付订单数", left: "center" },
      tooltip: { trigger: "axis" },
      xAxis: { type: "category", data: orderDates },
      yAxis: { type: "value" },
      series: [{ name: "订单数", type: "line", data: orderCounts }],
    };
    myChart2.setOption(option);
  }
}

function initChart3() {
  if (chart3.value) {
    myChart3 = echarts.init(chart3.value);
    const option = {
      title: { text: "近 7 日每日注册新用户数", left: "center" },
      tooltip: { trigger: "axis" },
      xAxis: { type: "category", data: userDates },
      yAxis: { type: "value" },
      series: [{ name: "新用户数", type: "line", data: userCounts }],
    };
    myChart3.setOption(option);
  }
}

function initChart4() {
  if (chart4.value) {
    myChart4 = echarts.init(chart4.value);
    const option = {
      title: { text: "热门商品 TOP3", left: "center" },
      tooltip: { trigger: "item" },
      series: [{ name: "销量", type: "pie", radius: "50%", data: topProducts }],
    };
    myChart4.setOption(option);
  }
}

function resizeCharts() {
  myChart1?.resize();
  myChart2?.resize();
  myChart3?.resize();
  myChart4?.resize();
}

onMounted(() => {
  initChart1();
  initChart2();
  initChart3();
  initChart4();
  window.addEventListener("resize", resizeCharts);
});

onUnmounted(() => {
  myChart1?.dispose();
  myChart2?.dispose();
  myChart3?.dispose();
  myChart4?.dispose();
  window.removeEventListener("resize", resizeCharts);
});
</script>

<style lang="scss" scoped>
.dashboard-container {
  position: relative;
  padding: 20px;

  .github-corner {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    border: 0;
  }

  .chart-card {
    margin-bottom: 16px;
  }

  .chart-header {
    font-size: 16px;
    font-weight: bold;
  }

  .version-item {
    padding: 16px;
    margin-bottom: 12px;
    background: var(--el-fill-color-lighter);
    border-radius: 8px;
    transition: all 0.2s;

    &.latest-item {
      background: var(--el-color-primary-light-9);
      border: 1px solid var(--el-color-primary-light-5);
    }
    &:hover {
      transform: translateX(5px);
    }
    .version-content {
      margin-bottom: 12px;
      font-size: 13px;
      line-height: 1.5;
      color: var(--el-text-color-secondary);
    }
  }
}
</style>
