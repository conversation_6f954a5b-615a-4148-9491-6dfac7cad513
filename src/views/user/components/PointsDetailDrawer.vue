<template>
  <el-drawer
    v-model="visible"
    title="积分明细"
    size="1200px"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <div class="points-detail">
      <div class="user-info">
        <el-avatar :size="60" :src="getImageUrl(userInfo?.avatar)" />
        <div class="user-info__content">
          <div class="nickname">{{ userInfo?.nickName }}</div>
        </div>
        <el-button type="primary" @click="showAddForm = true" style="margin-left: auto">
          新增积分变更
        </el-button>
      </div>

      <el-dialog
        v-model="showAddForm"
        title="新增积分变更"
        width="500px"
        append-to-body
        @close="resetForm"
      >
        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="变动积分" prop="value">
            <el-input-number
              v-model="form.value"
              :min="-999999"
              :max="999999"
              :precision="0"
              placeholder="请输入积分变动值"
            />
          </el-form-item>
          <el-form-item label="变动说明" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入变动说明"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="showAddForm = false">取 消</el-button>
            <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
              确 定
            </el-button>
          </div>
        </template>
      </el-dialog>

      <el-table v-loading="loading" :data="recordList" border style="width: 100%">
        <el-table-column label="操作类型" prop="opType" min-width="200">
          <template #default="{ row }">
            <span>{{ row.opType === 1 ? "用户操作" : "系统操作" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="变动说明" prop="remark" min-width="200" />

        <el-table-column label="变动积分" prop="value" width="120" align="right">
          <template #default="{ row }">
            <span :class="{ 'points-add': row.value > 0, 'points-minus': row.value < 0 }">
              {{ row.value > 0 ? "+" : "" }}{{ row.value }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="变动时间" prop="createTime" width="180" />
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="fetchData"
      />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from "vue";
import { getImageUrl } from "@/utils/image";
import UserAPI from "@/api/user";
import { ElMessage } from "element-plus";
import type { FormItemRule } from "element-plus";

const props = defineProps<{
  modelValue: boolean;
  userInfo?: {
    id: number;
    nickName: string;
    avatar: string;
    integral: number;
  };
}>();

const emit = defineEmits(["update:modelValue", "update"]);

const visible = ref(false);
const loading = ref(false);
const recordList = ref<any[]>([]);
const total = ref(0);
const showAddForm = ref(false);
const submitLoading = ref(false);
const formRef = ref();

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  userId: 0,
  type: undefined as number | undefined,
});

const form = reactive({
  value: 0,
  remark: "",
});

const rules = reactive<Record<string, FormItemRule[]>>({
  value: [
    { required: true, message: "请输入变动积分", trigger: "blur" },
    { type: "number", message: "积分必须为数字", trigger: "blur" },
  ],
  remark: [
    { required: true, message: "请输入变动说明", trigger: "blur" },
    { min: 2, max: 100, message: "长度在 2 到 100 个字符", trigger: "blur" },
  ],
});

watch(
  () => props.modelValue,
  (val) => {
    visible.value = val;
    if (val && props.userInfo) {
      queryParams.userId = props.userInfo.id;
      fetchData();
    }
  }
);

watch(
  () => visible.value,
  (val) => {
    emit("update:modelValue", val);
  }
);

const fetchData = async () => {
  if (!props.userInfo?.id) return;

  loading.value = true;
  try {
    const res = await UserAPI.getAdminUserIntegralList({
      ...queryParams,
      userId: props.userInfo.id,
    });
    recordList.value = res.records;
    total.value = res.total;
  } finally {
    loading.value = false;
  }
};

const handleClose = () => {
  queryParams.pageNum = 1;
  queryParams.type = undefined;
  recordList.value = [];
  total.value = 0;
};

const resetForm = () => {
  form.value = 0;
  form.remark = "";
  formRef.value?.resetFields();
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      submitLoading.value = true;
      try {
        await UserAPI.updateUserIntegralApi({
          userId: Number(props.userInfo?.id),
          value: form.value,
          remark: form.remark,
        });
        ElMessage.success("添加成功");
        showAddForm.value = false;
        fetchData();
        // 通知父组件更新用户信息
        emit("update");
      } catch (error) {
        console.error(error);
      } finally {
        submitLoading.value = false;
      }
    }
  });
};
</script>

<style lang="scss" scoped>
.points-detail {
  padding: 0 20px;

  .user-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background-color: var(--el-bg-color-page);
    border-radius: 4px;

    &__content {
      margin-left: 16px;

      .nickname {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .points {
        color: var(--el-text-color-secondary);
      }
    }
  }

  .search-container {
    margin-bottom: 20px;
    padding: 20px;
    background-color: var(--el-bg-color);
    border-radius: 4px;
    box-shadow: var(--el-box-shadow-light);

    .search-buttons {
      margin-left: auto;
    }
  }

  .points-add {
    color: var(--el-color-success);
  }

  .points-minus {
    color: var(--el-color-danger);
  }
}
</style>
