<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
        <el-form-item prop="nickName" label="用户昵称" style="width: 300px">
          <el-input
            v-model="queryParams.nickName"
            placeholder="请输入用户昵称"
            clearable
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item class="search-buttons">
          <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
          <el-button icon="refresh" @click="handleResetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="hover" class="data-table">
      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        class="data-table__content"
      >
        <el-table-column label="用户ID" prop="id" width="100" />
        <el-table-column label="用户昵称" prop="nickName" min-width="120" />
        <el-table-column label="微信ID" prop="openId" min-width="120" />
        <el-table-column label="头像" prop="avatar" width="100" align="center">
          <template #default="{ row }">
            <el-avatar :size="40" :src="getImageUrl(row.avatar)" />
          </template>
        </el-table-column>
        <el-table-column label="积分" prop="integral" width="100" align="right" />
        <el-table-column fixed="right" label="操作" width="250">
          <template #default="{ row }">
            <el-button type="primary" size="small" link @click="handlePointsDetail(row)">
              积分明细
            </el-button>
            <el-button type="primary" size="small" link @click="handleBuyRecord(row)">
              购买记录
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="fetchData"
      />
    </el-card>

    <!-- 积分明细抽屉 -->
    <points-detail-drawer
      v-model="pointsDetailVisible"
      :user-info="currentUser"
      @update="fetchData"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { useRouter } from "vue-router";
import UserAPI from "@/api/user";
import { getImageUrl } from "@/utils/image";
import PointsDetailDrawer from "./components/PointsDetailDrawer.vue";

defineOptions({
  name: "User",
  inheritAttrs: false,
});

const router = useRouter();

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  id: "",
  nickName: "",
});

// 数据列表
const loading = ref(false);
const total = ref(0);
const pageData = ref<any[]>([]);
const queryFormRef = ref();

// 积分明细抽屉
const pointsDetailVisible = ref(false);
const currentUser = ref<any>(null);

// 查询
function handleQuery() {
  queryParams.pageNum = 1;
  fetchData();
}

// 重置查询
function handleResetQuery() {
  queryFormRef.value?.resetFields();
  queryParams.pageNum = 1;
  fetchData();
}

// 获取列表数据
const fetchData = async () => {
  loading.value = true;
  try {
    const res = await UserAPI.getAdminUserList(queryParams);
    pageData.value = res.records;
    total.value = res.total;
  } finally {
    loading.value = false;
  }
};

// 积分明细
const handlePointsDetail = (row: any) => {
  currentUser.value = row;
  pointsDetailVisible.value = true;
};

// 购买记录
const handleBuyRecord = (row: any) => {
  // 跳转订单列表页面
  router.push({
    path: "/order/index",
    query: {
      userId: row.id,
    },
  });
};

// 初始化
onMounted(() => {
  fetchData();
});
</script>

<style lang="scss" scoped>
.search-container {
  margin-bottom: 20px;
  padding: 20px;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  box-shadow: var(--el-box-shadow-light);
}

.search-buttons {
  margin-left: auto;
}

.data-table {
  margin-bottom: 20px;
}
</style>
