# 积分管理功能说明

## 功能概述

本文档描述了积分活动管理模块的增删改查功能实现。该模块允许管理员对积分活动进行完整的管理操作。

## 功能特性

### 1. 分页查询积分活动列表

- **接口路径**: `GET /api/admin/integral-activity/selectList`
- **功能描述**: 支持分页查询积分活动列表，支持多条件筛选
- **查询条件**:
  - 活动名称（模糊查询）
  - 奖励积分
  - 限制人数
  - 过期时间范围

### 2. 查询积分活动详情

- **接口路径**: `GET /api/admin/integral-activity/detail/{id}`
- **功能描述**: 根据ID查询积分活动的详细信息

### 3. 创建积分活动

- **接口路径**: `POST /api/admin/integral-activity/create`
- **功能描述**: 创建新的积分活动
- **必填字段**:
  - 奖励积分（必须大于0）
  - 过期时间（不能早于当前时间）
- **可选字段**:
  - 限制人数（默认为0，表示不限制）
  - 活动描述

### 4. 更新积分活动

- **接口路径**: `PUT /api/admin/integral-activity/update`
- **功能描述**: 更新现有积分活动信息
- **验证规则**:
  - 积分活动必须存在
  - 过期时间不能早于当前时间
  - 保持原有的已使用次数

### 5. 删除积分活动

- **接口路径**: `DELETE /api/admin/integral-activity/delete/{id}`
- **功能描述**: 逻辑删除积分活动（软删除）

## 数据模型

### 积分活动实体 (AppIntegralActivityEntity)

```java
public class AppIntegralActivityEntity extends BaseEntity {
    private Integer integral;        // 奖励积分
    private LocalDateTime exprTime;  // 过期时间
    private Integer limitNum;        // 限制人数，0表示不限制
    private Integer usedCount;       // 已使用次数
    // 继承自BaseEntity的字段：id, createTime, updateTime, createUser, updateUser, remark, deleted
}
```

### 请求/响应模型

#### 查询请求 (AdminIntegralActivityRequest)

- 继承分页参数
- 支持按活动名称、积分、人数限制、时间范围筛选

#### 创建请求 (CreateIntegralActivityRequest)

- 奖励积分（必填，最小值1）
- 过期时间（必填）
- 限制人数（可选，默认0）
- 活动描述（可选）

#### 更新请求 (UpdateIntegralActivityRequest)

- ID（必填）
- 其他字段同创建请求

#### 响应模型 (AdminIntegralActivityResponse)

- 包含完整的积分活动信息
- 时间字段格式化为 "yyyy-MM-dd HH:mm:ss"

## 业务规则

1. **时间验证**: 过期时间不能早于当前时间
2. **积分验证**: 奖励积分必须大于0
3. **人数限制**: 限制人数不能小于0，0表示不限制
4. **软删除**: 删除操作为逻辑删除，不会物理删除数据
5. **已使用次数**: 更新时保持原有的已使用次数不变

## 异常处理

- 参数验证失败时抛出 `BusinessException`
- 积分活动不存在时抛出 `BusinessException`
- 时间验证失败时抛出 `BusinessException`
- 全局异常处理器统一处理异常并返回标准响应格式

## 技术实现

### 架构层次

1. **Controller层**: `AdminIntegralActivityController` - 处理HTTP请求
2. **Service层**: `AppIntegralActivityService` - 业务逻辑处理
3. **Repository层**: `AppIntegralActivityRepository` - 数据访问
4. **Mapper层**: `AppIntegralActivityMapper` - MyBatis映射

### 关键技术

- **MyBatis-Plus**: 简化数据库操作
- **Bean Validation**: 参数校验
- **分页查询**: 支持大数据量分页
- **对象转换**: 使用BeanUtil进行对象属性复制
- **统一响应**: 使用Result包装响应数据

## 测试

提供了完整的单元测试类 `AppIntegralActivityServiceTest`，覆盖主要业务场景：

- 创建积分活动测试
- 分页查询测试
- 更新积分活动测试

## API示例

### 创建积分活动

```json
POST /api/admin/integral-activity/create
{
    "integral": 100,
    "exprTime": "2025-07-30 23:59:59",
    "limitNum": 50,
    "remark": "新用户注册奖励活动"
}
```

### 查询积分活动列表

```json
GET /api/admin/integral-activity/selectList?pageNum=1&pageSize=10&remark=注册
```

### 更新积分活动

```json
PUT /api/admin/integral-activity/update
{
    "id": 1,
    "integral": 200,
    "exprTime": "2025-08-30 23:59:59",
    "limitNum": 100,
    "remark": "更新后的注册奖励活动"
}
```

## 注意事项

1. 所有时间字段使用 `LocalDateTime` 类型
2. 分页查询默认按ID倒序排列
3. 创建和更新操作都会进行参数验证
4. 删除操作为软删除，可通过数据库恢复
5. 已使用次数字段在更新时会被保护，不会被覆盖
